// 图片项目
const picture = {
  path: '/picture',
  // redirect: '/home',
  redirect: '/index', // TODO
  component: () => import('@/layout/index.vue'),
  children: [
    {
      path: 'index',
      name: 'index',
      component: () => import('@/views/Index/index.vue'),
    },
    {
      path: 'convert',
      name: 'convert',
      component: () => import('@/views/PictureConvert/index.vue'),
    },
    {
      path: 'raw',
      name: 'raw',
      component: () => import('@/views/PictureConvert/index.vue'),
    },
    {
      path: 'heic',
      name: 'heic',
      component: () => import('@/views/PictureConvert/index.vue'),
    },
    {
      path: 'livp',
      name: 'livp',
      component: () => import('@/views/PictureConvert/index.vue'),
    },
    {
      path: 'compress',
      name: 'compress',
      component: () => import('@/views/PictureCompress/index.vue'),
    },
    {
      path: 'matting',
      name: 'matting',
      component: () => import('@/views/Matting/index.vue'),
    },
    {
      path: 'watermark',
      name: 'watermark',
      component: () => import('@/views/PictureWatermark/index.vue'),
    },
    {
      path: 'pdf',
      name: 'pdf',
      component: () => import('@/views/PicturePdf/index.vue'),
    },
    {
      path: 'enlarge',
      name: 'enlarge',
      component: () => import('@/views/PictureALi/index.vue'),
    },
    {
      path: 'oldPicture',
      name: 'oldPicture',
      component: () => import('@/views/PictureALi/index.vue'),
    },
    {
      path: 'repair',
      name: 'repair',
      component: () => import('@/views/PictureALi/index.vue'),
    },
    {
      path: 'cartoon',
      name: 'cartoon',
      component: () => import('@/views/PictureALi/index.vue'),
    },
    {
      path: 'sketch',
      name: 'sketch',
      component: () => import('@/views/PictureALi/index.vue'),
    },

    // 批处理
    {
      path: 'changeSize',
      name: 'changeSize',
      component: () => import('@/views/Batch/ChangeSize/index.vue'),
    },
    {
      path: 'crop',
      name: 'crop',
      component: () => import('@/views/Batch/Crop/index.vue'),
    },
    {
      path: 'rename',
      name: 'rename',
      component: () => import('@/views/Batch/Rename/index.vue'),
    },
    {
      path: 'rotate',
      name: 'rotate',
      component: () => import('@/views/Batch/Rotate/index.vue'),
    },
    {
      path: 'classify',
      name: 'classify',
      component: () => import('@/views/Batch/Classify/index.vue'),
    },
    // 拼图
    {
      path: 'puzzle',
      name: 'puzzle',
      component: () => import('@/views/Puzzle/index.vue'),
    },
    // 证件照
    {
      path: 'passport',
      name: 'passport',
      component: () => import('@/views/Batch/Classify/index.vue'),
    },
  ],
}

export default picture
