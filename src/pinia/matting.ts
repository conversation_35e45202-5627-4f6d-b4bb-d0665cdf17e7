import { defineStore } from 'pinia'
import { messageSimple } from '@/utils/message'
// import { ElMessage, ElMessageBox } from 'element-plus'
import { useCommonStore } from '@/pinia/common'
import { getImageInfo, webUpload, fileToBase64ByReader } from '@/utils'
import { mapToArray } from '@/utils/util'
import { pageType } from '@/utils/enum'
import { chooseWebImages, dragHandler, onDragChange } from '@/utils/drag'
import { imgWatermarkExts } from '@/utils/enum/configData'
import { goLogin, goPay } from '@/utils/user'
import { getListAll, getListAllByBackground } from '@/api/batch'
import type Cropper from 'cropperjs'

let currentImgSize = 0
export const useMattingStore = defineStore({
  id: 'matting',
  state: () => ({
    imgsMap: new Map(),
    currentIndex: 0,
    categoryList: [] as any, // 尺寸列表
    categoryBackgroundList: [] as any, // 背景列表
    currentSegment: '换背景', // 当前segment：'手动精修', '换背景', '改尺寸'
    // changeSegmenting: false, // 是否正在切换 segment
    segments: [
      '手动精修',
      '换背景',
      '改尺寸'
    ],
    segmentsType: { custom: '手动精修', changeBackground: '换背景', changeSize: '改尺寸' },
    scaleFactor: 2, // 按scaleFactor进行放大（待定）
    // 手动精修相关状态
    brushType: 'keep', // 画笔类型：保留(keep)或擦除(erase)
    brushSize: 20, // 画笔大小
    edgeSoftness: 0, // 边缘柔和度
    maskHistory: [] as string[], // 蒙版历史记录
    maskHistoryIndex: -1, // 当前历史记录索引
    maskZoom: 1, // 蒙版缩放比例
    maskDragMode: false, // 是否处于拖动模式
    currentImg: {
      // 第一层 为水印，不需要变量承接
      mattingImage: '', // 第二层 抠图后的图片
      // 第三层
      backgroundColor: '', // 抠图后添加的背景色 backgroundColor backgroundImage 互斥
      backgroundImage: '', // 抠图后添加的背景图片 backgroundColor backgroundImage 互斥
      mattingCropImage: '', // 待裁剪图片：一二三层拼合后的图片，手动进行裁剪
      mattingMaskImage: '', // 手动抠图区域图，用于手动精修
      mattingOriginalMaskImage: '', // 原始蒙版图像，用于重置
      record: {
        // transform: `rotate(${currentImg.record.rotate}deg) scaleX(${currentImg.record.scaleX}) scaleY(${currentImg.record.scaleY})`,
        opacity: 100, // 透明度，0-100
        rotate: 0, // 旋转，0-360
        zoom: 1, // 缩放（兼容旧代码）
        scaleX: 1, // X轴缩放
        scaleY: 1, // Y轴缩放
        transformX: 1, // 左右翻转
        transformY: 1, // 上下翻转
        translate: { x: 0, y: 0 }, // 位移
        // sizeType: 'customSize',
        // customWidth: '',
        // customHeight: '',
        width: 0,
        height: 0,
      },
    } as any,
    cropper: null as Cropper | null,
    cropperRef: null as any,
    option: {
      guides: false, // 是否显示网格线
      movable: false, // 是否可以移动图片
      zoomable: false, // 是否可以缩放图片（以图片左上角为原点进行缩放）
      // 当 autoCrop属性 被设置为 true时, crop 事件将在 ready 事件之前被触发.
      // 当设置了 data 属性时, 另一个 crop 事件将在 ready 事件之前被触发.
      autoCrop: true, // 是否自动裁剪
      autoCropArea: 1, // 默认裁剪区域大小，值为0-1，表示裁剪框占图片的比例，1表示裁剪框与图片一样大
      cropBoxMovable: true, // 是否允许通过拖动来移动裁剪框
      cropBoxResizable: false, // 是否允许通过拖动来调整裁剪框的大小
      // 'crop': 创建一个新的裁剪框
      // 'move': 图片容器可移动
      // 'none': 什么也不做
      dragMode: 'none',
      // 0: 没有限制
      // 1: 限制裁剪框不超过图片容器的范围。
      // 2: 限制最片容器尺寸以在裁剪容器中展示。 如果图片容器和裁剪容器的比例不同，则图片容器以cover模式填充（图片容器保持原有比例，最长边和裁剪容器大小一致，短边等比缩放，可能会有部分区域不可见)。
      // 3: 限制图片容器尺寸以在裁剪器中展示。 如果图片容器和裁剪容器的比例不同，则图片容器以contain模式填充（图片容器保持原有比例，最短边和裁剪容器大小一直，长边等比缩放，可能会有留白）。
      viewMode: 2,
      minCropBoxWidth: 25,
      minCropBoxHeight: 25,
    },
    pageType: '',
    applyToAll: false,
    loading: false,
    showSearchResult: true,
    isOrigin: false, // 是否是原图
  }),
  getters: {},
  actions: {
    // 手动精修相关方法
    updateBrushType(type: string) {
      console.log('[MattingStore] 更新画笔类型:', type)
      this.brushType = type
      console.log('[MattingStore] 画笔类型已更新为:', this.brushType)
    },

    updateBrushSize(size: number) {
      console.log('[MattingStore] 更新画笔大小:', size)
      this.brushSize = size
      console.log('[MattingStore] 画笔大小已更新为:', this.brushSize)
    },

    updateEdgeSoftness(softness: number) {
      console.log('[MattingStore] 更新边缘柔和度:', softness)
      this.edgeSoftness = softness
      console.log('[MattingStore] 边缘柔和度已更新为:', this.edgeSoftness)
    },

    // 添加蒙版历史记录
    addMaskHistory(maskData: string) {
      console.log('[MattingStore] 添加蒙版历史记录, 当前索引:', this.maskHistoryIndex, '历史长度:', this.maskHistory.length)

      // 如果当前不是最新状态，则删除当前位置之后的所有历史
      if (this.maskHistoryIndex < this.maskHistory.length - 1) {
        const removedCount = this.maskHistory.length - 1 - this.maskHistoryIndex
        this.maskHistory = this.maskHistory.slice(0, this.maskHistoryIndex + 1)
        console.log('[MattingStore] 删除了', removedCount, '条后续历史记录')
      }

      // 添加新的历史记录
      this.maskHistory.push(maskData)
      this.maskHistoryIndex = this.maskHistory.length - 1
      console.log('[MattingStore] 蒙版历史记录已添加, 新索引:', this.maskHistoryIndex, '新长度:', this.maskHistory.length)
    },

    // 应用历史记录中的蒙版
    applyMaskHistory(index: number) {
      console.log('[MattingStore] 应用历史记录中的蒙版, 索引:', index, '历史长度:', this.maskHistory.length)

      if (index >= 0 && index < this.maskHistory.length) {
        this.maskHistoryIndex = index
        this.currentImg.mattingMaskImage = this.maskHistory[index]
        console.log('[MattingStore] 蒙版历史记录已应用, 当前索引:', this.maskHistoryIndex)

        // 应用蒙版后更新合成图像
        this.createMattingWithMask()
      } else {
        console.warn('[MattingStore] 无效的历史记录索引:', index, '有效范围: 0 -', this.maskHistory.length - 1)
      }
    },

    // 重置蒙版
    resetMask() {
      console.log('[MattingStore] 重置蒙版')

      if (this.currentImg.mattingOriginalMaskImage) {
        this.currentImg.mattingMaskImage = this.currentImg.mattingOriginalMaskImage
        // 清空历史记录
        this.maskHistory = [this.currentImg.mattingOriginalMaskImage]
        this.maskHistoryIndex = 0
        console.log('[MattingStore] 蒙版已重置, 历史记录已清空')

        // 重置蒙版后更新合成图像
        this.createMattingWithMask()
      } else {
        console.warn('[MattingStore] 无法重置蒙版，原始蒙版图像不存在')
      }
    },

    // 缩放蒙版
    zoomMask(scale: number) {
      const newZoom = Math.max(0.4, Math.min(5, this.maskZoom + scale)) // 限制缩放范围在 0.4 到 5 之间
      this.maskZoom = newZoom
      console.log('[MattingStore] 蒙版缩放已更新为:', this.maskZoom)

      // 触发画布重绘 - 通过更新相关的 DOM 样式
      this.updateCanvasZoom()
    },

    // 重置缩放
    resetZoomMask() {
      console.log('[MattingStore] 重置蒙版缩放, 当前缩放:', this.maskZoom)
      this.maskZoom = 1
      console.log('[MattingStore] 蒙版缩放已重置为:', this.maskZoom)

      // 触发画布重绘
      this.updateCanvasZoom()
    },

    // 更新画布缩放（现在由 Moveable 控制，这里只更新样式类）
    updateCanvasZoom() {
      console.log('[MattingStore] 更新画布缩放, 当前缩放:', this.maskZoom)

      // 查找手动精修画布元素，只更新样式类，不操作 transform
      const canvas = document.querySelector('.manual-refine-canvas') as HTMLCanvasElement
      if (canvas) {
        // 根据拖动模式更新样式类
        if (this.maskDragMode) {
          canvas.classList.add('drag-mode')
        } else {
          canvas.classList.remove('drag-mode')
        }

        console.log('[MattingStore] 画布样式类已更新, 拖动模式:', this.maskDragMode)
      } else {
        console.warn('[MattingStore] 未找到手动精修画布元素')
      }

      // 同时更新 origin-box 的样式类
      const originBox = document.querySelector('.origin-box') as HTMLElement
      if (originBox) {
        if (this.maskDragMode) {
          originBox.style.cursor = 'grab'
        } else {
          originBox.style.cursor = 'default'
        }
        console.log('[MattingStore] origin-box 样式已更新')
      }
    },

    // 切换拖动模式
    toggleDragMode(isDragging: boolean) {
      console.log('[MattingStore] 切换拖动模式:', isDragging)
      this.maskDragMode = isDragging
      console.log('[MattingStore] 拖动模式已更新为:', this.maskDragMode)

      // 更新画布样式以反映拖动模式的变化
      this.updateCanvasZoom()
    },

    // 创建带蒙版的合成图像
    createMattingWithMask() {
      console.log('[MattingStore] 开始创建带蒙版的合成图像')

      if (!this.currentImg.mattingImage || !this.currentImg.mattingMaskImage) {
        console.warn('[MattingStore] 无法创建合成图像，缺少必要的图像数据:', {
          mattingImage: !!this.currentImg.mattingImage,
          mattingMaskImage: !!this.currentImg.mattingMaskImage
        })
        return
      }

      // 创建画布
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (!ctx) return

      // 加载原始抠图图像
      const mattingImg = new Image()
      mattingImg.crossOrigin = 'anonymous'

      mattingImg.onload = () => {
        console.log('[MattingStore] 抠图图像加载完成, 尺寸:', mattingImg.width, 'x', mattingImg.height)

        // 设置画布尺寸
        canvas.width = mattingImg.width
        canvas.height = mattingImg.height

        // 加载蒙版图像
        const maskImg = new Image()
        maskImg.crossOrigin = 'anonymous'

        maskImg.onload = () => {
          console.log('[MattingStore] 蒙版图像加载完成, 尺寸:', maskImg.width, 'x', maskImg.height)

          // 绘制原始抠图图像
          ctx.drawImage(mattingImg, 0, 0)

          // 获取图像数据
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
          const data = imageData.data

          // 获取蒙版数据
          ctx.clearRect(0, 0, canvas.width, canvas.height)
          ctx.drawImage(maskImg, 0, 0)
          const maskData = ctx.getImageData(0, 0, canvas.width, canvas.height).data

          console.log('[MattingStore] 开始应用蒙版到图像')

          // 应用蒙版到图像的 alpha 通道
          for (let i = 0; i < data.length; i += 4) {
            // 蒙版是黑白图像，白色表示保留，黑色表示透明
            const maskValue = maskData[i] // 使用蒙版的红色通道

            // 如果原图像素已经是透明的，保持透明
            if (data[i + 3] === 0) {
              continue
            }

            // 根据蒙版值设置透明度
            // 白色(255)保留，黑色(0)透明，灰色按比例
            data[i + 3] = Math.round((data[i + 3] * maskValue) / 255)
          }

          // 将处理后的图像数据放回画布
          ctx.putImageData(imageData, 0, 0)

          // 更新抠图图像
          this.currentImg.mattingImage = canvas.toDataURL('image/png')
          console.log('[MattingStore] 带蒙版的合成图像创建完成')

          // 同步更新到改尺寸模块 - 触发重新生成组合图
          // 通过触发 mattingImage 的变化来更新改尺寸模块
          const event = new CustomEvent('mattingImageUpdated')
          window.dispatchEvent(event)
          console.log('[MattingStore] 已触发改尺寸模块更新事件')
        }

        maskImg.onerror = () => {
          console.error('[MattingStore] 蒙版图像加载失败')
        }

        maskImg.src = this.currentImg.mattingMaskImage
      }

      mattingImg.onerror = () => {
        console.error('[MattingStore] 抠图图像加载失败')
      }

      mattingImg.src = this.currentImg.mattingImage
    },
    init(name: any) {
      // this.loading = false
      this.pageType = pageType[name as keyof typeof pageType]
      this.bindEvent()
      setTimeout(this.dragHandler, 1000)
    },
    bindEvent() {
      // 选择文件或图片结果返回
      this.unBindEvent()
      window.ipcRendererApi?.on('select-callback', () => (this.loading = true))
    },
    unBindEvent() {
      window.ipcRendererApi?.removeAllListeners('select-callback')
    },
    clear() {
      this.imgsMap.clear()
    },
    /** 获取拖拽的文件 */
    dragHandler() {
      dragHandler(this.imageInfoHandler)
    },
    /** 获取拖拽的文件夹 */
    onDragChange(e: any) {
      onDragChange(e, this.imageInfoHandler)
    },
    customWebUpload(e: any) {
      this.loading = true
      webUpload(e, 'customMattingInput')
    },
    customChooseWebImages(res: any) {
      this.loading = false
      console.log(res.target.files)
      // TODO 上传图片到阿里云，并添加到 customArr 里面
      // 临时使用本地图片
      fileToBase64ByReader(res.target.files[0], (base64: any) => {
        this.currentImg.backgroundImage = base64
      })
    },
    /** web端选择图片 */
    chooseWebImages(choose: any = {}) {
      this.loading = true
      // this.imgsMap.clear() // 清空所有图片，仅支持单张
      chooseWebImages(choose, this.imageInfoHandler)
    },
    getCurrent() {
      const mapArray = Array.from(this.imgsMap.entries())
      if (!mapArray.length) return

      const [, target] = mapArray[this.currentIndex]
      this.currentImg = target

      return target
    },
    /** 获取图片信息，返回缩略图、原始图宽高 */
    async getImageInfo(content: File | Blob) {
      if (!content) return Promise.resolve({})
      return getImageInfo(content)
    },
    /** 获取图片信息，并添加到 Map imgsMap 里面 */
    async imageInfoHandler(result: any) {
      if (!result) return
      this.loading = true

      const commonStore = useCommonStore()
      const filePath = commonStore.filePath
      const showWatermark = commonStore.watermark()

      // TODO can limit 张数
      // res.length = 30
      // messageSimple.warning('最多使用30张图片')

      // result.forEach(async (res: any) => {})
      for (const res of result) {
        const { key, fileBuffer } = res
        let { file } = res

        const filenameFull = window.isMac
          ? key.split('/').pop()
          : key.split('\\').pop()
        const originFormat = filenameFull.split('.').pop()

        if (imgWatermarkExts.includes(originFormat.toLowerCase())) {
          if (!this.imgsMap.has(key)) {
            const filename = filenameFull.replace(`.${originFormat}`, '')
            let blob: any = null

            if (fileBuffer) {
              // 仅electron使用
              blob = new Blob([fileBuffer], {
                type: `image/${originFormat}`,
              })
            }
            file = file || blob || ''
            // if (!file.size) return messageSimple.error('文件大小不能为0kb')
            const imgInfo: any =
              (file.size && (await this.getImageInfo(file))) || {}
            const newFormat = imgInfo.format || 'jpg'
            const imgUrl = imgInfo.thumbnail || '' // 缩略图
            const newFile = `${filePath}${filename}.${originFormat}`

            let pictureParams: any = {
              key, // 图片路径key，如：                            /Users/<USER>/Desktop/***/1、首页.jpg
              filename, // 文件名，不含后缀，如：                   1、首页
              newFileName: filename, // 新的文件名，支持修改，如：  1、首页custom
              newFile, // 新的图片路径，如：                       /Users/<USER>/Desktop/图片转换器/1、首页.jpg
              originFile: key, // 原始图片路径，如：               /Users/<USER>/Desktop/***/1、首页.jpg
              record: {
                originFormat, // 原始图片后缀，如：                jpg
                newFormat, // 新的图片后缀，如：                   jpg
                status: 'wait',
                loading: false,
                imgUrl, // 缩略图
                name: '自由', // 尺寸选中名字
                width: imgInfo.width || 0, // 原始图片宽度，如：   1920
                height: imgInfo.height || 0, // 原始图片高度，如：   1080

                // img.record.aspectRatio = currentImg.value.record.aspectRatio; // 记录比例
                // img.record.autoCropWidth = currentImg.value.record.autoCropWidth; // 自动裁剪宽度
                // img.record.autoCropHeight = currentImg.value.record.autoCropHeight; // 自动裁剪高度
                // img.record.pixel_with = currentImg.value.record.pixel_with; // 记录输出尺寸
                // img.record.pixel_height = currentImg.value.record.pixel_height; // 记录输出尺寸
                // img.record.scale = currentImg.value.record.scale; // 记录缩放
                // img.record.name = currentImg.value.name; // 记录尺寸选中名字

                opacity: 1, // 透明度，0-100
                rotate: 0, // 旋转，0-360
                zoom: 1, // 缩放
                transformX: 1, // 左右翻转
                transformY: 1, // 上下翻转
              },
              checked: true,
              showWatermark, // 是否打水印
              file,
            }
            // fileToCanvas(file, (result: any) => {
            //   pictureParams.image = result.image
            // })

            this.imgsMap.set(key, pictureParams)
          } else {
            messageSimple.warning('已过滤同名文件！')
          }
        } else {
          console.log('已过滤不支持的文件格式：' + originFormat)
          messageSimple.warning('已过滤不支持的文件格式：' + originFormat)
        }
      }
      // console.log('imgsMap', this.imgsMap)

      this.imgsMap.size > 1 && this.imgsMap.delete(this.currentImg.key) // 删除当前图片，仅支持单张
      this.getCurrent()
      this.loading = false
    },
    beforeConvert(type: string) {
      if (goLogin()) return

      const commonStore = useCommonStore()
      const showWatermark = commonStore.watermark()
      showWatermark ? goPay() : this.convert(type)
    },
    async convert(type: any) {
      // if (!window.isElectron) {
      //   return messageSimple.warning('需要下载app')
      // }

      this.loading = true

      // // 切换到改尺寸模块，确保裁剪器初始化
      // const currentSegment = this.currentSegment
      // this.currentSegment = this.segmentsType.changeSize // 改尺寸
      // this.changeSegmenting = true
      // // 等待裁剪器初始化和渲染完成
      // await new Promise((resolve) => {
      //   // 检查裁剪器是否已初始化
      //   const checkCropper = () => {
      //     if (this.cropper && !this.changeSegmenting) {
      //       // 裁剪器已初始化且渲染完成
      //       console.log('Cropper is ready for export')
      //       resolve(true)
      //     } else {
      //       // 继续等待
      //       console.log('Waiting for cropper to initialize...')
      //       setTimeout(checkCropper, 100)
      //     }
      //   }
      //   // 开始检查
      //   setTimeout(checkCropper, 300)
      // })
      // 额外等待一点时间确保渲染完全
      // await new Promise((resolve) => setTimeout(() => resolve(true), 200))

      let tempCurrentIndex = this.currentIndex
      let imgs: any = new Map()
      let index = 0
      const isSingleImage = type === 'current'

      // mark crop
      for (const img of this.imgsMap) {
        const [, key] = img // [key, proxy]

        // const options = key.record.pixel_with
        //   ? {
        //       width: key.record.pixel_with,
        //       height: key.record.pixel_height,
        //     }
        //   : {}
        // mark crop 未固定宽高，则增加等待输出图片的清晰度
        // {
        //   width: key.record.autoCropWidth * 2,
        //   height: key.record.autoCropHeight * 2,
        // }
        // console.log('cropper options', options)

        // options 导出图片的尺寸设置
        const options = {
          width: key.record.pixel_with || key.record.width,
          height: key.record.pixel_height || key.record.height,
        }
        // 含背景图时的宽高
        if (key.backgroundImage && key.record.name === '自由') {
          options.width = key.record.autoCropWidth
          options.height = key.record.autoCropHeight
        }
        // 设置比例时，按scaleFactor进行放大
        if (key.record.name === '自由' || key.record.name.includes(':')) {
          options.width = options.width * this.scaleFactor
          options.height = options.height * this.scaleFactor
        }
        // console.log('options', options);

        const sizeKB = key.file.size / 1024
        const isLargeImg = sizeKB > 1024 * 5
        let exportFormat = isLargeImg ? 'image/jpeg' : 'image/webp'
        if (isSingleImage) {
          if (index === this.currentIndex) {
            // web端特殊处理
            if (!window.isElectron ) {
              exportFormat = key.backgroundColor || key.backgroundImage ? 'image/jpeg' : 'image/png'
            }
            const base64 = this.cropper
              ?.getCroppedCanvas(options)
              .toDataURL(exportFormat, 0.9)
            // this.currentImg.mattingCropImage // 当前未改尺寸的图片，未压缩
            key.base64 = base64
            imgs.set(key.key, {
              key: key.key,
              record: { ...key.record },
              base64: key.base64,
              originFile: key.originFile,
              newFile: key.newFile,
              filename: key.filename,
              showWatermark: key.showWatermark,
            })
          }
        } else {
          this.currentIndex = index
          // mark crop 大图增加等待时间
          let timeout = 500
          if (sizeKB > 1024 * 1) timeout = sizeKB / 2
          if (sizeKB > 1024 * 5) timeout = sizeKB / 3
          if (sizeKB > 1024 * 10) timeout = sizeKB / 4
          if (sizeKB > 1024 * 20) timeout = sizeKB / 6
          if (sizeKB > 1024 * 40) timeout = sizeKB / 8
          await new Promise((resolve) =>
            setTimeout(() => resolve(true), timeout)
          )
          // console.log('timeout', timeout)
          // this.downSimple()

          // 如果自己画（废弃）：1、img拉伸 2、按比例裁剪时需计算裁剪宽高 3、canvas画
          // var img = new Image()
          // img.crossOrigin = 'Anonymous'
          // img.onload = function () {
          //   ctx.drawImage(
          //     img,
          //     item.style.left,
          //     item.style.top,
          //     item.style.width,
          //     item.style.height
          //   )
          // }
          // img.src = item.img

          const base64 = this.cropper
            ?.getCroppedCanvas(options)
            .toDataURL(exportFormat, 0.9)
          key.base64 = base64
          imgs.set(key.key, {
            key: key.key,
            record: { ...key.record },
            base64: key.base64,
            originFile: key.originFile,
            newFile: key.newFile,
            filename: key.filename,
            showWatermark: key.showWatermark,
          })
        }
        index++
      }
      currentImgSize = isSingleImage ? 1 : imgs.size
      this.currentIndex = tempCurrentIndex // 切换回当前图片
      // this.currentSegment = currentSegment // 切换回当前模块

      if (!window.isElectron) {
        imgs.forEach((img: any) => {
          // console.log(img.base64)
          var aLink: any = document.createElement('a')
          aLink.download = img.filename || 'demo'
          aLink.href = img.base64
          aLink.click()
        })
        this.loading = false
      } else {
        this.batchSave(imgs)
      }
    },
    batchSave(targetImgsMap: any) {
      window.ipcRendererApi?.send('batch-base64-img-to-file', {
        maps: [...mapToArray(targetImgsMap)],
        options: {},
      })
    },
    batchSaveCallback() {
      if (!window.isElectron) return
      const _this = this
      window.ipcRendererApi?.removeAllListeners(
        'batch-base64-img-to-file-progress'
      )
      window.ipcRendererApi?.on(
        'batch-base64-img-to-file-progress',
        (event: any, res: any) => {
          console.log(res)
          currentImgSize--
          if (currentImgSize === 0) {
            messageSimple.success('转换成功')
            _this.loading = false

            const commonStore = useCommonStore()
            commonStore.showConvertSuccessDialog = true
          }
        }
      )
    },
    downSimple(type?: string) {
      var aLink: any = document.createElement('a')
      aLink.download = this.currentImg.filename || 'demo'

      if (type === 'blob') {
        this.cropper?.getCroppedCanvas().toBlob((data: any) => {
          aLink.href = window.URL.createObjectURL(data)
          aLink.click()
        })
      } else {
        const base64 = this.cropper?.getCroppedCanvas().toDataURL()
        aLink.href = base64
        aLink.click()
      }
    },
    getListAll(params?: any) {
      const isSearch = !!params
      if (!params?.name && sessionStorage.categoryList) {
        this.categoryList = JSON.parse(sessionStorage.categoryList)
        this.showSearchResult = !!this.categoryList.length
        return
      }
      getListAll(params)
        .then((_: any) => {
          if (_.data.code === 0 && _.data.data.length > 0) {
            let categoryList: any = [
              {
                id: 0,
                name: '自定义',
                list: [],
              },
            ]

            _.data.data.forEach((item: any) => {
              categoryList.push({
                id: item.category.id,
                name: item.category.name,
                status: item.category.status,
                sticky: item.category.name.sticky,
                list: item.size,
              })
            })

            this.categoryList = categoryList
            !isSearch && this.updateCategorySession()
          } else {
            this.categoryList = []
            console.log(_)
            // messageSimple.warning('未搜索到结果')
          }
          if (isSearch) this.showSearchResult = !!this.categoryList.length
        })
        .catch((e) => {
          messageSimple.error('搜索异常：', e.message)
          console.log(e)
        })
    },
    getListAllByBackground(params?: any) {
      const isSearch = !!params
      if (!params?.name && sessionStorage.categoryBackgroundList) {
        this.categoryBackgroundList = JSON.parse(
          sessionStorage.categoryBackgroundList
        )
        return
      }
      getListAllByBackground(params)
        .then((_: any) => {
          if (_.data.code === 0 && _.data.data.length > 0) {
            let categoryBackgroundList: any = [
              {
                id: 0,
                name: '自定义',
                list: [],
              },
            ]

            _.data.data.forEach((item: any) => {
              categoryBackgroundList.push({
                id: item.category.id,
                name: item.category.name,
                status: item.category.status,
                list: item.background,
              })
            })

            this.categoryBackgroundList = categoryBackgroundList
            !isSearch && this.updateCategorySession()
          } else {
            this.categoryBackgroundList = []
            console.log(_)
            // messageSimple.warning('未搜索到结果')
          }
          if (isSearch)
            this.showSearchResult = !!this.categoryBackgroundList.length
        })
        .catch((e) => {
          messageSimple.error('搜索异常：', e.message)
          console.log(e)
        })
    },
    updateCategorySession() {
      this.categoryList.length &&
        (sessionStorage.categoryList = JSON.stringify(this.categoryList))

      this.categoryBackgroundList.length &&
        (sessionStorage.categoryBackgroundList = JSON.stringify(
          this.categoryBackgroundList
        ))
    },
    updateCropBoxData() {
      if (!this.cropper) return
      const cropBoxData = this.cropper.getCropBoxData()
      this.currentImg.record.left = cropBoxData.left
      this.currentImg.record.top = cropBoxData.top
      this.currentImg.record.autoCropWidth = cropBoxData.width
      this.currentImg.record.autoCropHeight = cropBoxData.height
      // console.log('updateCropBoxData', cropBoxData)
    },
  },
})
