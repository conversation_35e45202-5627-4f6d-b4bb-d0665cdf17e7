<template>
  <div
    class="sidebar-container"
    v-scroll-effect
    :style="{
      top: isIframe ? 0 : '48px',
    }"
  >
    <div
      class="module flex"
      v-for="{ name, icon, type, marginTop } of list"
      :class="{
        current: pageType === type,
      }"
      :style="{
        marginTop: marginTop || 0,
      }"
      @click="goNext(type)"
    >
      <img class="icon list" :src="`${icon}@2x.png`" />
      <p>{{ name }}</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { pageType as pageEnum } from '@/utils/enum'
import { messageSimple } from '@/utils/message'
import { usePictureStore } from '@/pinia/picture'
import { VERSION } from '@/utils/configData/config'
import bridge from '@/utils/bridge'
const pictureStore = usePictureStore()
const route = useRoute()
const router = useRouter()
const isIframe = window.isIframe

const key = route.path.split('/').reverse()[0] || 'list'
const pageTypeValue = pageEnum[key as keyof typeof pageEnum]
const pageType = ref(pageTypeValue)

let list = [
  {
    name: '通用格式转换',
    icon: '/img/pic/second/icon_Image_format_conversion',
    type: pageEnum.convert,
    marginTop: '9px',
  },
  {
    name: '图片压缩',
    icon: '/img/pic/second/icon_zip',
    type: pageEnum.compress,
  },
  {
    name: 'raw转换',
    icon: '/img/pic/second/icon_raw',
    type: pageEnum.raw,
  },
  {
    name: 'heic转换',
    icon: '/img/pic/second/icon_heic',
    type: pageEnum.heic,
  },
  {
    name: 'livp转换',
    icon: '/img/pic/second/icon_livp',
    type: pageEnum.livp,
  },
  {
    name: '智能抠图',
    icon: '/img/pic/second2/icon_znkt',
    type: pageEnum.matting,
  },
  // {
  //   name: '写真模版',
  //   icon: '/img/pic/second2/icon_xzmb',
  //   type: pageEnum.photoTemplate,
  // },
  // {
  //   name: '拼图',
  //   icon: '/img/pic/second2/icon_pt',
  //   type: pageEnum.puzzle,
  // },
  // {
  //   name: '证件照',
  //   icon: '/img/pic/second2/icon_zjz',
  //   type: pageEnum.passport,
  // },
  {
    name: '图片加水印',
    icon: '/img/pic/second/icon_tpjsy',
    type: pageEnum.watermark,
  },
  {
    name: '图片转PDF',
    icon: '/img/pic/second/icon_pdf',
    type: pageEnum.pdf,
  },
  {
    name: '图片无损放大',
    icon: '/img/pic/second/icon_wsfd',
    type: pageEnum.enlarge,
  },
  {
    name: '老照片上色',
    icon: '/img/pic/second/icon_lzpss',
    type: pageEnum.oldPicture,
  },
  {
    name: '模糊人脸修复',
    icon: '/img/pic/second/icon_mhrlxf',
    type: pageEnum.repair,
  },
  {
    name: '人物动漫化',
    icon: '/img/pic/second/icon_rwdmh',
    type: pageEnum.cartoon,
  },
  {
    name: '人像素描',
    icon: '/img/pic/second/icon_rxsm',
    type: pageEnum.sketch,
  },
  {
    name: '批量改尺寸',
    icon: '/img/pic/second2/icon_plgcc',
    type: pageEnum.changeSize,
  },
  {
    name: '批量裁剪',
    icon: '/img/pic/second2/icon_pljc',
    type: pageEnum.crop,
  },
  {
    name: '批量重命名',
    icon: '/img/pic/second2/icon_plcmm',
    type: pageEnum.rename,
  },
  {
    name: '批量旋转',
    icon: '/img/pic/second2/icon_plxz',
    type: pageEnum.rotate,
  },
  {
    name: '批量分类',
    icon: '/img/pic/second2/icon_plfl',
    type: pageEnum.classify,
  },
]
if (window.isElectron && bridge.compareVersions(VERSION, '*******') <= 0) {
  list = list.filter((item) => {
    return !item.name.includes('批量')
  })
}

const goNext = (targetType: pageEnum) => {
  if (targetType === pageType.value) return
  if (pictureStore.hasLoading())
    return messageSimple.warning('当前页面图片处理中，请稍后')

  router.push({
    name: targetType,
  })

  nextTick(() => {
    pageType.value = targetType
  })
}
</script>

<style lang="scss">
.sidebar-container {
  user-select: none;

  .el-tree-node {
    padding: 2px 0;
  }
  .el-tree-node__content {
    height: 36px;
    padding-right: 12px;
    border-radius: 4px;

    .el-icon {
      margin-left: 12px;
    }

    .el-tree-node__label {
      display: flex;
      width: 100%;
      overflow: hidden;
    }
  }
  .el-tree-node__children {
    padding: 1px 0;
  }
  .el-tree--highlight-current
    .el-tree-node.is-current
    > .el-tree-node__content {
    height: 36px;
    background-color: rgb(237, 237, 237);
  }
  .el-tree-node__content:hover {
    height: 36px;
    border-radius: 4px;
    background-color: rgb(237, 237, 237, 0.5);
  }
}
</style>
<style lang="scss" scoped="scoped">
.sidebar-container {
  position: fixed;
  top: 48px;
  bottom: 0;
  left: 0;
  width: 157px;
  height: calc(100% - 48px);
  font-size: 14px;
  color: #666;
  border-right: 1px solid #e0e0e0;
  background-color: #fff;
  overflow-x: hidden;
  overflow-y: auto;
  z-index: 1001;

  .icon-wrap {
    .icon {
      margin: 26px 0 0 24px;
      width: 151px;
      height: 32px;
    }
  }

  // .search {
  //   position: relative;
  //   left: 24px;
  //   width: 192px;
  //   height: 40px;
  //   margin-top: 30px;
  //   background: #ffffff;
  //   border-radius: 4px;
  // }

  .module {
    position: relative;
    width: 137px;
    height: 39px;
    margin: 9px 10px;
    padding: 7px 12px;
    cursor: pointer;

    .docArrow {
      position: absolute;
      left: 10px;
      top: 12px;
    }

    .icon {
      position: relative;
      top: 2.5px;
      width: 20px;
      height: 20px;
      margin-right: 4px;
    }

    p {
      line-height: 25px;
    }

    .line {
      position: absolute;
      left: 12px;
      top: -5px;
      width: 192px;
      height: 1px;
      border: 0.5px solid #e0e0e0;
    }

    &:hover {
      background-color: rgb(237, 237, 237, 0.5);
      border-radius: 4px;
    }
  }

  .current {
    background: #f0f7fe;
    border-radius: 4px;
    border-left: 3px solid #5599f6;
    padding-left: 9px;
  }

  .tree {
    position: relative;
    width: 157px;
    margin: 0 0 15px 12px;
    // padding: 0 0 0 12px;
    font-size: 14px;
    max-height: 500px;
    overflow-y: auto;
    color: #333333;
    background-color: #f7f8f9;
    cursor: pointer;

    .folder {
      position: relative;
      width: 24px;
      height: 24px;
    }

    .dotdotdot1 {
      padding-left: 8px;
      line-height: 24px;
    }
  }
}
</style>
