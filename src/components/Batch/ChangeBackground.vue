<template>
  <div class="change-background-component">
    <!-- 大小设置 -->
    <div class="title flex mt24 hide">
      <div class="left flex1">大小</div>
    </div>
    <!-- <div class="custom-size-input-box flex hide">
      <el-input
        type="text"
        :min="1"
        :max="99999"
        :minlength="1"
        :maxlength="5"
        oninput="value=value.replace(/[^0-9]/g,'')"
        v-model="currentStore.currentImg.record.customWidth"
        placeholder="请输入宽"
      >
        <template #prefix> <div class="tip">宽</div> </template>
      </el-input>
      <img src="/img/pic/second2/<EMAIL>" alt="" class="link" />
      <el-input
        type="text"
        :min="1"
        :max="99999"
        :minlength="1"
        :maxlength="5"
        oninput="value=value.replace(/[^0-9]/g,'')"
        v-model="currentStore.currentImg.record.customHeight"
        placeholder="请输入高"
      >
        <template #prefix> <div class="tip">高</div> </template>
      </el-input>
    </div> -->

    <!-- 颜色设置 -->
    <div class="title flex mt24">
      <div class="left flex1">颜色</div>
    </div>
    <div class="color-picker-box">
      <div class="color-list">
        <div
          v-for="color in colorList"
          :key="color"
          class="color-item"
          :class="{ active: currentStore.currentImg.backgroundColor === color }"
          :style="{
            border: color === 'transparent' ? '1px solid #EAEAEA' : '',
            backgroundColor: color,
          }"
          @click="changeColor(color)"
        ></div>

        <el-popover
          ref="popover"
          placement="bottom"
          trigger="click"
          :offset="0"
          :show-arrow="false"
          popper-class="textEditBoxPopover customerPopover"
        >
          <template #reference>
            <div
              class="color-item"
              :class="{ active: currentStore.currentImg.backgroundColor && !colorList.includes(currentStore.currentImg.backgroundColor) }"
            ></div>
          </template>
          <template #default>
            <Color
              :color="currentStore.currentImg.backgroundColor"
              moduleType="multiColor"
              type="close"
              @change="changeColor"
            ></Color>
          </template>
        </el-popover>
      </div>
    </div>

    <!-- 图片设置 -->
    <div class="title flex mt24 mb12">
      <div class="left flex1">图片</div>
    </div>
    <ChangeBackgroundByCategory
      ref="changeBackgroundByCategoryRef"
      :currentStore="currentStore"
    />
  </div>
</template>

<script setup lang="ts">
import { useMattingStore } from '@/pinia/matting'
import { pageType as pageTypeEnum } from '@/utils/enum'
import { useRoute } from 'vue-router'
import ChangeBackgroundByCategory from './ChangeBackgroundByCategory.vue'

const route = useRoute()

// 获取当前应该使用的 store
const getStore = () => {
  switch (route.name) {
    case pageTypeEnum.matting:
      return useMattingStore()
    default:
      return useMattingStore()
  }
}
const currentStore = getStore()

// 颜色选择
const colorList = [
  'transparent',
  '#EE3F27',
  '#F6B35C',
  '#F17D2F',
  '#F8F84F',
  '#5DD840',
  '#6AF6F4',
  '#2B7BF7',
]

const changeColor = (color: any) => {
  if (currentStore.currentImg) {
    currentStore.currentImg.backgroundColor = color
    currentStore.currentImg.backgroundImage= ''

    setTimeout(() => {
      if (currentStore.currentImg.record?.name === '自由') {
        delete currentStore.currentImg.record.left
        delete currentStore.currentImg.record.top
        delete currentStore.currentImg.record.autoCropWidth
        delete currentStore.currentImg.record.autoCropHeight
        // console.log('currentStore.currentImg.record', currentStore.currentImg.record);
      }
    }, 100)
  }
}
</script>

<style lang="scss">
.textEditBoxPopover {
  width: auto !important;
  padding: 12px 0 !important;
}
</style>
<style lang="scss" scoped>
.change-background-component {
  height: 100%;
  .title {
    font-weight: 600;
    font-size: 14px;
    color: #000000;
    line-height: 20px;

    .right {
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      line-height: 20px;

      .width,
      .height {
        width: 60px;
      }
      .height {
        margin-left: 10px;
      }
    }
  }

  .custom-size-input-box {
    margin-top: 12px;

    .link {
      position: relative;
      top: 3px;
      margin: 0 10px;
      width: 26px;
      height: 26px;
    }

    .tip {
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      line-height: 20px;
      margin-right: 20px;
    }

    .el-input {
      width: 99px !important;
      height: 32px;
      .el-input__wrapper {
        background: #ffffff;
        border-radius: 2px;
        border: 1px solid #e0e0e0;
        box-shadow: none;

        .el-input__inner {
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          line-height: 20px;
        }
      }
    }
  }

  // 颜色选择器样式
  .color-picker-box {
    margin-top: 12px;

    .color-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .color-item {
        position: relative;
        width: 20px;
        height: 20px;
        border-radius: 3px;
        cursor: pointer;

        &.active {
          outline: 1px solid #006eff;
        }

        &:first-child {
          background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAADBJREFUOE9jfPXq1X8GPEBUVBSfNAPjqAHDIgz+//+PNx28fv0afzoYNYCBceiHAQAmfVXZBZk9KQAAAABJRU5ErkJggg==);
          background-size: 9px 9px;
        }
        &:last-child {
          background-image: url('/img/pic/second2/caise.png');
          background-size: contain;
        }
      }
    }
  }
}
</style>
