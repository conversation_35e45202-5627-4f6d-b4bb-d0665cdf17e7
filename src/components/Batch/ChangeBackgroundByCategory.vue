<template>
  <div class="change-background-category">
    <!-- 分类列表 -->
    <section class="category-box">
      <div class="tabs flex">
        <a
          v-for="item in currentStore.categoryBackgroundList"
          class="tab"
          :key="item.id"
          :class="{
            selected: currentTypeId == item.id,
          }"
          :data-id="item.id"
          :data-name="item.name"
          :href="`#background_${item.id}${item.name}`"
          @click="(event: any) => handleTabClick(item, event)"
        >
          {{ item.name }}
        </a>
      </div>
      <div class="category" v-scroll-effect>
        <div
          class="module"
          v-for="item in currentStore.categoryBackgroundList"
          :key="item.id"
        >
          <div
            v-if="item.name === '自定义' || item.list?.length"
            class="title"
            :name="`background_${item.id}${item.name}`"
            :id="`background_${item.id}${item.name}`"
            :data-id="item.id"
          >
            {{ item.name }}
          </div>
          <div v-if="item.name === '自定义'" class="custom flex">
            <div
              class="btn center"
              v-for="custom in customArr"
              :key="custom.name"
              @click="currentStore.customWebUpload"
            >
              <img
                class="icon"
                src="/img/pic/second2/<EMAIL>"
                alt=""
              />
              <span>{{ custom.name }}</span>
            </div>
          </div>
          <div class="list" v-masonry transition-duration="0.3s" item-selector=".item" gutter="8">
            <!-- Masonry items -->
            <div
              v-for="content in item.list"
              :key="content.name"
              class="item"
              v-masonry-tile
              :class="{
                selected: currentImg.record.name === content.name,
              }"
              @click="chooseBackgroundImage(content)"
            >
              <img
                class="chessboard-bg"
                :src="`${STATICDOMAIN}/${content.thumbnail}`"
                alt=""
                @load="refreshMasonry"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, inject, watch, defineProps } from 'vue'
import { useMattingStore } from '@/pinia/matting'
import { pageType as pageTypeEnum } from '@/utils/enum'
import { STATICDOMAIN } from '@/utils/configData/config'
import { getInstance } from '@/utils/tabsScroll'
import { useRoute } from 'vue-router'

// Define props to receive currentStore from parent component
defineProps({
  currentStore: {
    type: Object,
    default: () => ({}),
  },
})

const customArr = [{ name: '上传图片' }]
const route = useRoute()

// 获取当前应该使用的 store
const getStore = () => {
  switch (route.name) {
    case pageTypeEnum.matting:
      return useMattingStore()
    default:
      return useMattingStore()
  }
}

// currentStore 是响应式的（因为是getStore拿到的是store本身，是响应式对象的引用）
const currentStore = getStore()

// 使用计算属性来获取当前 store 的状态
// currentImg 是非响应式的（因为是currentStore里面的属性，会丢失响应式，所以加上computed）
// const currentImg = currentStore.currentImg
const currentImg: any = computed(() => currentStore.currentImg)

// 创建TabsScroll实例
const tabsScroller = getInstance('background')
const currentTypeId = tabsScroller.currentTypeId

// 处理标签点击
const handleTabClick = (item: any, event: Event) => {
  tabsScroller.tabClick(item, event)
}

// 初始化获取列表
currentStore.getListAllByBackground()

const chooseBackgroundImage = (custom: any) => {
  currentImg.value.backgroundColor = ''
  currentImg.value.backgroundImage = `${STATICDOMAIN}/${custom.thumbnail}`

  // When background image changes, we need to ensure the cropper is reinitialized with full-screen selection
  setTimeout(() => {
    if (currentImg.value?.record?.name === '自由') {
      delete currentImg.value.record.left
      delete currentImg.value.record.top
      delete currentImg.value.record.autoCropWidth
      delete currentImg.value.record.autoCropHeight
    }
    // console.log('currentImg.value.record', currentImg.value.record);
  }, 100)
}

// Refresh masonry layout when an image loads
// Get the redrawVueMasonry function from the Vue Masonry plugin
const redrawVueMasonry = inject('redrawVueMasonry')

const refreshMasonry = () => {
  // Use setTimeout to ensure the DOM has updated
  setTimeout(() => {
    if (typeof redrawVueMasonry === 'function') {
      // Use the injected function
      redrawVueMasonry()
    } else {
      console.warn('redrawVueMasonry function not available')
    }
  }, 100)
}

// Watch for changes in currentSegment，need rerender
watch(() => currentStore.currentSegment, (newSegment) => {
  if (newSegment === currentStore.segmentsType.changeBackground) {
    refreshMasonry()
  }
})

onMounted(() => {
  // Initialize the TabsScroll instance with specific selectors for this component
  tabsScroller.init(
    '.change-background-category .category-box .tabs',
    '.change-background-category .category-box .category',
    260 - 75
  )
})

onUnmounted(() => {
  // Clean up the TabsScroll instance
  tabsScroller.cleanup()
})
</script>

<style lang="scss" scoped>
.change-background-category {
  display: flex;
  flex-direction: column;
  height: 100%;
  user-select: none;

  .category-box {
    flex: 1;
    height: calc(100% - 50px);
    width: calc(249px + 12px);
    overflow: hidden;

    .tabs {
      flex-wrap: wrap;

      .tab {
        margin: 0 12px 12px 0;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        cursor: pointer;

        padding: 3px 7px;
        border: 1px solid #eaeaea;
        border-radius: 4px;

        &:hover {
          opacity: 0.7;
        }
      }
      .selected {
        color: #006eff;
        border: 1px solid #006eff;
      }
    }

    .category {
      position: relative;
      height: calc(100% - 82px - 250px + 160px); // 75px
      width: 249px;
      overflow-x: hidden;
      overflow-y: auto;
      padding-right: 8px; /* Add padding to account for scrollbar */

      .custom {
        flex-wrap: wrap;

        .btn {
          margin-bottom: 12px;
          height: 36px;
          width: 100%;
          background: #389bfd;
          border-radius: 6px;
          border: 0;

          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          line-height: 20px;
          cursor: pointer;

          img {
            position: relative;
            top: -2px;
            margin-right: 4px;
            width: 16px;
            height: 16px;
          }

          &:hover {
            opacity: 0.7;
          }
        }

        .text {
          padding: 6px 0 6px 11px;

          img {
            width: 16px;
            height: 14px;
            margin-right: 4px;
          }
        }
        .img {
          padding: 6px 0 6px 7px;
          margin-left: 12px;

          img {
            width: 18px;
            height: 16px;
            margin-right: 6px;
          }
        }
      }

      .module {
        .title {
          margin-bottom: 12px;
          font-weight: 400;
          font-size: 13px;
          color: #333333;
          line-height: 18px;
        }

        .list {
          width: 100%;
          margin-bottom: 4px;
          user-select: none;
          position: relative;

          .item {
            margin-bottom: 8px;
            width: calc(50% - 4px); /* Two columns with 8px gutter */
            border-radius: 4px;
            border: 1px solid #fff;
            cursor: pointer;
            overflow: hidden;
            box-sizing: border-box;

            img {
              width: 100%;
              display: block;
              border-radius: 4px;

              // -webkit-app-region: no-drag;
              -webkit-user-drag: none;
            }

            &:hover {
              opacity: 0.7;
            }
          }

          .selected {
            border: 1px solid #006eff;
          }
        }
      }
    }
  }
}
</style>
