<template>
  <div class="common-header flex">
    <div class="flex1">
      <div class="flex">
        <div class="flex back-wrap" @click="back">
          <img class="back-img" src="/img/pic/second/<EMAIL>" alt="" />
          <p class="back-text">返回</p>
        </div>
        <el-button
          v-if="props.showReselect"
          type="primary"
          link
          @click="(e: any) => upload(e)"
        >
          重新选择
        </el-button>
      </div>
    </div>
    <div
      v-if="isElectron"
      class="export-directory flex"
      @click="showExportDirectoryDialog = true"
    >
      <img class="img" src="/img/pic/second2/<EMAIL>" alt="" />
      <p>导出目录</p>
    </div>
    <div class="export flex" @click="exportImg">
      <img class="img" src="/img/pic/second2/<EMAIL>" alt="" />
      <p>立即导出</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { goLogin, goPay } from '@/utils/user'
import { storeToRefs } from 'pinia'
import { useCommonStore } from '@/pinia/common'
import { messageSimple } from '@/utils/message'
const commonStore = useCommonStore()
commonStore.getPath()
const { proxy } = getCurrentInstance()
const isElectron = window.isElectron
let { showExportDirectoryDialog } = storeToRefs(commonStore)

const props = defineProps({
  showReselect: {
    type: Object,
    default: true,
  },
  currentStore: {
    type: Object,
    default: {},
  },
  imgExts: {
    type: Array,
    default: [],
  },
  imgsMap: {
    type: Object,
    default: {},
  },
  callback: {
    type: Function,
    default: () => {},
  },
})

const back = () => {
  proxy.$messageBox
    .confirm(' ', '确定返回吗？', {
      distinguishCancelAndClose: true,
      confirmButtonText: '确定', // 保留
      cancelButtonText: '取消', // 不保留
      customClass: 'customStyleByMessageBox',
      type: 'warning',
      // center: true
    })
    .then((e: any) => {
      if (e === 'confirm') {
        props.imgsMap.clear()
      }
    })
    .catch((e: any) => {})
}

const upload = (e: any) => {
  commonStore.webUpload(e)
}

const exportImg = () => {
  if (goLogin()) return
  if (goPay()) return

  // const url = pageInfo.value[pageType.value].currentImg.new
  if (!props.currentStore.currentImg.mattingImage) {
    return messageSimple.warning('抠图中，请稍等')
  }
  proxy.$filter.tools.debounce(() => {
    props.currentStore.beforeConvert('current')
  }, 300)()
}
</script>

<style lang="scss" scoped>
.common-header {
  margin-right: 309px !important;
  // height: 24px;
  // margin-bottom: 20px;

  height: 20px;
  margin: 20px 0 31px 20px;
  user-select: none;

  .back-wrap {
    cursor: pointer;
    &:hover {
      opacity: 0.7;
    }
    .back-img {
      width: 24px;
      height: 24px;
    }
    .back-text {
      font-size: 14px;
      color: #666666;
      line-height: 24px;
    }
  }

  .el-button {
    height: 24px;
    margin-left: 16px;
    padding: 0;
    font-weight: 400;
    font-size: 14px;
    color: #389bfd;
    line-height: 24px;
    border-color: transparent !important;
    &:hover {
      opacity: 0.7;
    }
  }

  .export-directory {
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 24px;
    cursor: pointer;
    &:hover {
      opacity: 0.7;
    }

    .img {
      position: relative;
      top: 2px;
      margin-right: 4px;
      width: 20px;
      height: 20px;
    }
  }

  .export {
    margin-left: 24px;
    font-weight: 400;
    font-size: 14px;
    color: #389bfd;
    line-height: 24px;
    cursor: pointer;
    &:hover {
      opacity: 0.7;
    }

    .img {
      position: relative;
      top: 2.5px;
      margin-right: 4px;
      width: 20px;
      height: 20px;
    }
  }
}
</style>
